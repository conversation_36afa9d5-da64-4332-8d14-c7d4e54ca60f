import logging
import time
import csv
import clash_api
from ip_checker import <PERSON><PERSON><PERSON><PERSON>
from config import config

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)

def check_proxy_ip(proxy_name):
    """检查单个代理的IP信息"""
    try:
        # 切换到指定代理
        if clash_api.select_proxy(proxy_name):
            time.sleep(2)  # 等待切换完成

            # 使用系统代理检查IP（假设ClashVerge设置了系统代理）
            # 或者使用Clash的混合端口
            proxy_config = {
                "http": "http://127.0.0.1:7890",
                "https": "http://127.0.0.1:7890"
            }

            # 创建IP检查器实例
            checker = IPChecker(proxy_config)

            # 获取IP信息
            ipv4 = checker.fetch_ipv4()
            if ipv4:
                ip_details = checker.fetch_ip_details(ipv4)
                ip_risk = checker.fetch_ip_risk(ipv4)
                ping0_risk = checker.fetch_ping0_risk(ipv4)

                # 合并所有信息
                ip_info = {
                    'name': proxy_name,
                    'ipv4': ipv4,
                    'ipv6': None,  # 暂时跳过IPv6
                }

                if ip_details:
                    ip_info.update(ip_details)
                if ip_risk:
                    ip_info.update(ip_risk)
                if ping0_risk:
                    ip_info.update(ping0_risk)

                logging.info(f"Successfully checked {proxy_name}: {ipv4}")
                return ip_info
            else:
                logging.warning(f"Failed to get IP for {proxy_name}")
        else:
            logging.warning(f"Failed to select proxy {proxy_name}")
    except Exception as e:
        logging.error(f"Error checking proxy {proxy_name}: {e}")

    return None

def main():
    logging.info("Starting simplified IP checker")

    # 获取所有代理（已经过滤过的代理名称列表）
    real_proxies = clash_api.fetch_proxies()
    if not real_proxies:
        logging.error("No proxies found")
        return

    logging.info(f"Found {len(real_proxies)} real proxy nodes")
    
    # 检查每个代理的IP
    ip_infos = []
    for i, proxy_name in enumerate(real_proxies[:10]):  # 限制前10个代理避免太慢
        logging.info(f"Checking proxy {i+1}/{min(10, len(real_proxies))}: {proxy_name}")
        ip_info = check_proxy_ip(proxy_name)
        if ip_info:
            ip_infos.append(ip_info)
        
        # 添加延迟避免请求过快
        time.sleep(1)
    
    # 保存结果到CSV
    if ip_infos:
        fieldnames = [
            'name', 'ipv4', 'ipv6', 'country', 'countryCode', 'region', 
            'regionName', 'city', 'zip', 'lat', 'lon', 'timezone', 
            'isp', 'org', 'as', 'score', 'risk', 'ping0Risk', 'ipType', 'nativeIP'
        ]
        
        with open('ip_infos_simple.csv', 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for ip_info in ip_infos:
                writer.writerow(ip_info)
        
        logging.info(f"Results saved to ip_infos_simple.csv with {len(ip_infos)} entries")
    else:
        logging.warning("No IP information collected")

if __name__ == "__main__":
    main()
